spring:
  datasource:
    url: *****************************************
    username: postgres
    password: root
    driver-class-name: org.postgresql.Driver
    hikari:
      auto-commit: true
      pool-name: ora
      minimum-idle: 10
      maximum-pool-size: 10
      connection-timeout: 10000
      allow-pool-suspension: false
      isolate-internal-queries: false
      read-only: false
      register-mbeans: false
      validation-timeout: 1000
      leak-detection-threshold: 1200000
  jpa:
    show-sql: true
    open-in-view: false
    hibernate:
      ddl-auto: update
    properties:
      hibernate:
        jdbc:
          batch_size: 50
        order_updates: true
        order_inserts: true
        session_factory:
        format_sql: false
  #          statement_inspector: io.github.clive.luxomssystem.infrastructure.config.JpaAuthInspector
  data:
    redis:
      url: redis://localhost:6379
      username: default
      jedis:
        pool:
          max-active: 100
          max-wait: -1
          max-idle: 100
          min-idle: 10
      timeout: 5000
      repositories:
        enabled: false
      database: 1

system:
  external-base-url: "http://localhost:5173"
