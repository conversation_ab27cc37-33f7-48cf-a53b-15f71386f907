package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.Address
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.DeclareProductInfo
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.DeliverTypeInfo
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.FpxApiRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.FpxApiService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.LogisticsServiceInfo
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.Parcel
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.ReturnInfo
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.req.FpxWayBillCancelRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.fpx.req.FpxWayBillRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.OkHttpClient
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service

@Service
class FpxPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val fpxApiService: FpxApiService,
    private val waybillRepository: WaybillRepository,
) : OrderPredictionRemoteService {
    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "FPX 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }
        val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }

        val totalPrice =
            waybillRequests.sumOf { order ->
                order.price(waybillRequests.sumOf { it.qty }, order.country)
            }

        val fpxApiRequest =
            FpxApiRequest(
                refNo = waybill.orderNo,
                businessType = "BDS",
                dutyType = "U",
                cargoType = "5",
                isInsure = "N",
                currencyFreight = "USD",
                logisticsServiceInfo =
                    LogisticsServiceInfo(
                        logisticsProductCode = waybill.shipping.shipMethod,
                        customsService = "N",
                        signatureService = "N",
                    ),
                parcelList =
                    listOf(
                        Parcel(
                            weight = waybillRequests.sumOf { it.weight },
                            includeBattery = "N",
                            parcelValue = totalPrice.toDouble(),
                            currency = "USD",
                            declareProductInfo =
                                waybillRequests.map { order ->
                                    DeclareProductInfo(
                                        declareProductNameEn = order.name,
                                        declareProductNameCn = order.cnName,
                                        declareProductCodeQty = order.qty,
                                        declareUnitPriceImport =
                                            order
                                                .price(waybillRequests.sumOf { it.qty }, order.country)
                                                .toDouble(),
                                        declareUnitPriceExport =
                                            order
                                                .price(waybillRequests.sumOf { it.qty }, order.country)
                                                .toDouble(),
                                        currencyExport = "USD",
                                        currencyImport = "USD",
                                        brandExport = "none",
                                        brandImport = "none",
                                        packageRemarks = order.skuCode(),
                                    )
                                },
                        ),
                    ),
                sender =
                    Address(
                        firstName = "DAMU",
                        country = "CN",
                        phone = "15356637391",
                        city = "杭州市",
                        district = "上城区",
                        street = "新塘路1233号 万事利科创中心 2号楼402",
                    ),
                recipientInfo =
                    Address(
                        firstName = waybill.recipient.receiverName,
                        lastName = "",
                        phone = waybill.recipient.phone,
                        country = waybill.recipient.country,
                        state = waybill.recipient.state,
                        city = waybill.recipient.city,
                        street = waybill.recipient.fullAddress(),
                        postCode = waybill.recipient.postcode,
                    ),
                returnInfo =
                    ReturnInfo(
                        isReturnOnDomestic = "U",
                        isReturnOnOversea = "U",
                    ),
                deliverTypeInfo =
                    DeliverTypeInfo(
                        deliverType = "1",
                    ),
            )

        try {
            log.info { "FPX 订单准备预报| 订单号: ${waybill.orderNo} | 请求参数: ${JSON.toJSONString(fpxApiRequest)}" }
            val response = fpxApiService.createOrder(fpxApiRequest)
            if (response.onSuccess()) {
                waybill.status = WayBillStatus.PENDING
                waybill.waybillNo = response.data?.fpxTrackingNo ?: ""
                log.info { "FPX 订单预报成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
                waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
                waybill.status = WayBillStatus.COMPLETED
                eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
                waybill.clearErrorMsg()
            } else {
                log.error { "FPX 订单预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${JSON.toJSONString(response)}" }
                waybill.failed(JSON.toJSONString(response.errors))
            }
        } catch (e: Exception) {
            log.error(e) { "FPX 订单预报异常 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
        }
        waybillRepository.saveAndFlush(waybill)
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        val request =
            FpxWayBillCancelRequest(
                requestNo = waybill.orderNo,
                cancelReason = "cancel",
            )

        return try {
            log.info { "FPX 订单取消预报 | 订单号: ${waybill.orderNo} | 请求参数: ${JSON.toJSONString(request)}" }
            val response = fpxApiService.cancelWaybill(request)
            if (!response.onSuccess()) {
                log.error { "FPX 订单取消预报失败 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 错误信息: ${JSON.toJSONString(response)}" }
            } else {
                log.info { "FPX 订单取消预报成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
            }
            response.onSuccess()
        } catch (e: Exception) {
            log.error(e) { "FPX 订单取消预报异常 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 错误信息: ${e.message}" }
            false
        }
    }

    override fun getPrintUrl(waybill: Waybill): String {
        val request = FpxWayBillRequest(requestNo = waybill.orderNo)

        try {
            log.info { "FPX 获取打印URL | 订单号: ${waybill.orderNo} | 请求参数: ${JSON.toJSONString(request)}" }
            val response = fpxApiService.getWaybill(request)
            if (response?.onSuccess() == true) {
                val labelUrl = response.data?.labelUrlInfo?.logisticsLabel
                if (labelUrl != null) {
                    return uploadWaybill(waybill, labelUrl)
                } else {
                    log.error { "FPX 获取打印URL为空 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 响应: ${JSON.toJSONString(response)}" }
                    return ""
                }
            } else {
                log.error { "FPX 获取打印URL失败 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 响应: ${JSON.toJSONString(response)}" }
                return ""
            }
        } catch (e: Exception) {
            log.error(e) { "FPX 获取打印URL异常 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 错误信息: ${e.message}" }
            return ""
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
