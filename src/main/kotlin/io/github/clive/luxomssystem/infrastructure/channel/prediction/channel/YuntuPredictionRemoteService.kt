package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import com.fasterxml.jackson.annotation.JsonProperty
import io.github.clive.luxomssystem.common.utils.EUCountryEnum
import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.Headers.Companion.toHeaders
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.math.BigDecimal
import java.util.*

@Service
class YunTuPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    @Value("\${yun-tu.account}") private val account: String,
    @Value("\${yun-tu.apiSecret}") private val apiSecret: String,
    @Value("\${yun-tu.api.waybill}") private val waybillUrl: String,
    @Value("\${yun-tu.api.print}") private val printUrl: String,
    @Value("\${yun-tu.api.delPrediction}") private val delPredictionUrl: String,
    private val waybillRepository: WaybillRepository,
) : OrderPredictionRemoteService {
    override fun createPredication(waybill: Waybill) {
        try {
            val orderList = loadSubOrder(waybill)
            if (orderList.isEmpty()) {
                log.error { "YunTu 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
                waybill.failed("No suborder found")
                return
            }
            val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
            val weight = waybillRequests.sumOf { it.weight }
            val reqYunTuPredictionDTO =
                ReqYunTuPredictionDTO(
                    taxNumber = waybill.taxNumber,
                    customerOrderNumber = waybill.orderNo,
                    shippingMethodCode = waybill.shipping.shipMethod ?: "",
                    packageCount = 1,
                    weight = if (weight == BigDecimal.ZERO) BigDecimal("0.01") else weight,
                    orderExtra =
                        if (EUCountryEnum.allKey.contains(waybill.recipient.country)) {
                            listOf(ReqYunTuOrderExtraDTO())
                        } else {
                            emptyList()
                        },
                    receiver =
                        ReqYunTuReceiveDTO(
                            firstName = waybill.recipient.receiverName ?: "",
                            city = waybill.recipient.city ?: "",
                            zip = waybill.recipient.postcode ?: "",
                            street = waybill.recipient.address(),
                            countryCode = waybill.recipient.country ?: "",
                            phone = waybill.recipient.phone ?: "",
                            state = waybill.recipient.state ?: "",
                        ),
                    parcels =
                        buildList {
                            for (req in waybillRequests) {
                                add(
                                    ReqYunTuProductDTO(
                                        quantity = req.qty,
                                        eName = req.name,
                                        cName = req.cnName,
                                        currencyCode = "USD",
                                        unitPrice = req.price(waybillRequests.sumOf { it.qty }, req.country),
                                        unitWeight = req.weight,
                                        remark = "${req.orderNo}-${req.skuCode()}",
                                        invoicePart = req.material,
                                        hsCode = req.hsCode,
                                    ),
                                )
                            }
                        },
                )
            // TODO         // 欧盟国家走附加服务

            val headers =
                mapOf(
                    "Authorization" to "Basic ${Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())}",
                )

            val body = JSON.toJSONString(listOf(reqYunTuPredictionDTO))
            log.info { "YunTu 创建运单预报 | 订单号: ${waybill.orderNo} | 请求参数: $body" }
            val response =
                okHttpClient
                    .newCall(
                        Request
                            .Builder()
                            .url(waybillUrl)
                            .post(body.toRequestBody(jsonMedia))
                            .headers(headers.toHeaders())
                            .build(),
                    ).execute()

            val responseBody = response.body?.string() ?: throw Exception("Empty response body")

            log.info { "YunTu 创建运单预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }
            val rstYunTuWayBillDTO = JSON.parseObject<RstYunTuWayBillDTO>(responseBody)

            if (rstYunTuWayBillDTO.code == "0000") {
                waybill.status = WayBillStatus.COMPLETED
                waybill.waybillNo = rstYunTuWayBillDTO.item[0].wayBillNumber
                waybill.trackingNumber = rstYunTuWayBillDTO.item[0].trackingNumber
                // Use the shared getPrintUrl method
                waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
                waybill.clearErrorMsg()
                eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
            } else {
                log.error { "YunTu 创建预报失败 | 订单号: ${waybill.orderNo} | 原因: ${rstYunTuWayBillDTO.message}" }
                waybill.run { failed(rstYunTuWayBillDTO.item[0].remark ?: rstYunTuWayBillDTO.message) }
            }
            waybillRepository.saveAndFlush(waybill)
        } catch (e: Exception) {
            log.error(e) { "YunTu 创建预报异常 | 订单号: ${waybill.orderNo} | 原因: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        val reqYunTuDelPredictionDTO =
            ReqYunTuDelPredictionDTO(
                orderType = "2",
                orderNumber = waybill.orderNo,
            )

        val headers =
            mapOf(
                "Authorization" to "Basic ${Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())}",
            )

        try {
            val response =
                okHttpClient
                    .newCall(
                        Request
                            .Builder()
                            .url(delPredictionUrl)
                            .post(JSON.toJSONString(reqYunTuDelPredictionDTO).toRequestBody(jsonMedia))
                            .headers(headers.toHeaders())
                            .build(),
                    ).execute()

            val responseBody = response.body?.string() ?: throw Exception("Empty response body")
            val rstYunTuDelPredictionDTO = JSON.parseObject<RstYunTuDelPredictionDTO>(responseBody)
            log.info { "YunTu 删除运单预报 | 订单号: ${waybill.orderNo} | 响应参数: $responseBody" }
            return rstYunTuDelPredictionDTO.code == "SUCCESS"
        } catch (e: Exception) {
            log.error(e) { "YunTu 删除运单预报异常 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            return false
        }
    }

    // Shared method to get print URL
    override fun getPrintUrl(waybill: Waybill): String {
        val headers =
            mapOf(
                "Authorization" to "Basic ${Base64.getEncoder().encodeToString("$account&$apiSecret".toByteArray())}",
            )

        val printResponse =
            okHttpClient
                .newCall(
                    Request
                        .Builder()
                        .url(printUrl)
                        .post(JSON.toJSONString(listOf(waybill.waybillNo)).toRequestBody(jsonMedia))
                        .headers(headers.toHeaders())
                        .build(),
                ).execute()

        val printResponseBody = printResponse.body?.string() ?: throw Exception("Empty print response body")
        log.info { "YunTu 获取打印URL | 订单号: ${waybill.orderNo} | 响应参数: $printResponseBody" }
        val rstYunTuPrintDTO = JSON.parseObject<RstYunTuPrintDTO>(printResponseBody)

        if (rstYunTuPrintDTO.item[0].orderInfos[0].code == 100) {
            val printUrl = rstYunTuPrintDTO.item[0].url
            val uploadWaybill = uploadWaybill(waybill, printUrl)
            log.info { "YunTu 获取打印URL成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 打印URL: $printUrl" }
            return uploadWaybill
        } else {
            log.error { "YunTu 获取打印URL失败 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo} | 错误信息: ${rstYunTuPrintDTO.message}" }
            throw Exception("Failed to get print URL")
        }
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}

// Data classes and enums remain the same as in the previous version

// Data classes for request and response objects
data class ReqYunTuPredictionDTO(
    val customerOrderNumber: String,
    val shippingMethodCode: String,
    val packageCount: Int,
    val weight: BigDecimal,
    val orderExtra: List<ReqYunTuOrderExtraDTO>,
    val receiver: ReqYunTuReceiveDTO,
    val parcels: List<ReqYunTuProductDTO>,
    val taxNumber: String? = null,
)

data class ReqYunTuOrderExtraDTO(
    val ExtraCode: String = "V1",
    val ExtraName: String = "云途预缴1",
)

data class ReqYunTuReceiveDTO(
    val firstName: String,
    val city: String,
    val zip: String,
    val street: String,
    val countryCode: String,
    val phone: String,
    val state: String,
)

data class ReqYunTuProductDTO(
    val quantity: Int,
    val eName: String,
    val cName: String,
    val hsCode: String?,
    val invoicePart: String?,
    val currencyCode: String,
    val unitPrice: BigDecimal,
    val unitWeight: BigDecimal,
    val remark: String,
)

data class RstYunTuWayBillDTO(
    @JsonProperty("Code") val code: String,
    @JsonProperty("Message") val message: String,
    @JsonProperty("Item") val item: List<RstYunTuWayBillItemDTO> = emptyList(),
    @JsonProperty("RequestId") val requestId: String,
    @JsonProperty("TimeStamp") val timeStamp: String,
)

data class RstYunTuWayBillItemDTO(
    @JsonProperty("CustomerOrderNumber") val customerOrderNumber: String?,
    @JsonProperty("Success") val success: Int,
    @JsonProperty("TrackType") val trackType: String?,
    @JsonProperty("Remark") val remark: String?,
    @JsonProperty("RequireSenderAddress") val requireSenderAddress: Int?,
    @JsonProperty("AgentNumber") val agentNumber: String?,
    @JsonProperty("WayBillNumber") val wayBillNumber: String?,
    @JsonProperty("TrackingNumber") val trackingNumber: String?,
    @JsonProperty("ShipperBoxs") val shipperBoxs: String?,
    @JsonProperty("BarCodes") val barCodes: String?,
    @JsonProperty("AddrType") val addrType: Int,
)

data class RstYunTuPrintDTO(
    @JsonProperty("Item") val item: List<RstYunTuPrintItemDTO>,
    @JsonProperty("Code") val code: String?,
    @JsonProperty("Message") val message: String?,
    @JsonProperty("RequestId") val requestId: String?,
    @JsonProperty("TimeStamp") val timeStamp: String?,
)

data class RstYunTuPrintItemDTO(
    @JsonProperty("Url") val url: String,
    @JsonProperty("LabelType") val labelType: String?,
    @JsonProperty("LabelString") val labelString: String?,
    @JsonProperty("OrderInfos") val orderInfos: List<RstYunTuPrintOrderInfoDTO> = emptyList(),
)

data class RstYunTuPrintOrderInfoDTO(
    @JsonProperty("CustomerOrderNumber") val customerOrderNumber: String?,
    @JsonProperty("Error") val error: String?,
    @JsonProperty("Code") val code: Int?,
)

data class ReqYunTuDelPredictionDTO(
    val orderType: String,
    val orderNumber: String,
)

data class RstYunTuDelPredictionDTO(
    @JsonProperty("Code")
    val code: String,
    @JsonProperty("Message")
    val message: String,
)
//
// fun main() {
//    val name = "xxx-21312"?.replace("[^a-zA-Z\\s]".toRegex(), "")
//    println(name)
// }
