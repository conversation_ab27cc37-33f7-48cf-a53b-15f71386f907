package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel

import io.github.clive.luxomssystem.common.utils.JSON
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.waybill.event.WaybillCompletedEvent
import io.github.clive.luxomssystem.domain.waybill.model.WayBillStatus
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import io.github.clive.luxomssystem.infrastructure.channel.cos.CosInnerRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.OrderPredictionRemoteService
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request.WaybillRequest
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenCreateWaybillResponse
import io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.yanwen.YanWenPrintUrlResponse
import io.github.clive.luxomssystem.infrastructure.repository.jpa.SubOrderRepository
import io.github.clive.luxomssystem.infrastructure.repository.jpa.WaybillRepository
import io.github.oshai.kotlinlogging.KotlinLogging
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.toRequestBody
import org.springframework.beans.factory.annotation.Value
import org.springframework.context.ApplicationEventPublisher
import org.springframework.stereotype.Service
import java.io.ByteArrayInputStream
import java.math.BigDecimal
import java.math.BigInteger
import java.security.MessageDigest
import java.util.*

@Service
class YanWenPredictionRemoteService(
    private val eventBus: ApplicationEventPublisher,
    override val ossClient: CosInnerRemoteService,
    override val okHttpClient: OkHttpClient,
    override val subOrderRepository: SubOrderRepository,
    private val waybillRepository: WaybillRepository,
    @Value("\${yan-wen-hz.account}") private val accountHz: String,
    @Value("\${yan-wen-hz.ApiSecret}") private val apiSecretHz: String,
    @Value("\${yan-wen-qz.account}") private val accountQz: String,
    @Value("\${yan-wen-qz.ApiSecret}") private val apiSecretQz: String,
    @Value("\${yan-wen-gz.account}") private val accountGz: String,
    @Value("\${yan-wen-gz.ApiSecret}") private val apiSecretGz: String,
    @Value("\${yan-wen-yw.account}") private val accountYw: String,
    @Value("\${yan-wen-yw.ApiSecret}") private val apiSecretYw: String,
    @Value("\${yan-wen.waybill}") private val waybillUrl: String,
) : OrderPredictionRemoteService {
    //    @EventListener(ApplicationReadyEvent::class)
//    fun init(event: ApplicationReadyEvent) {
//        val result = test("UK177265885YP", "YW_YW")
//        log.info { "url: $result" }
//    }

    override fun createPredication(waybill: Waybill) {
        val orderList = loadSubOrder(waybill)
        if (orderList.isEmpty()) {
            log.error { "YanWen 创建运单失败 | 订单号: ${waybill.orderNo} | 原因: 未找到子订单" }
            waybill.failed("No suborder found")
            return
        }

        val (account, apiSecret) = getAccountAndSecret(waybill.shipping.channel.name)
        try {
            val requestBody = buildRequestBody(waybill, orderList)
            log.info { "YanWen 创建运单请求 | 订单号: ${waybill.orderNo} | 请求参数: $requestBody" }
            val response = sendRequest(requestBody, account, apiSecret, "express.order.create")
            log.info { "YanWen 创建运单响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }
            handleResponse(response, waybill)
        } catch (e: Exception) {
            log.error(e) { "YanWen 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            waybill.failed(e.message ?: "Unknown error")
            waybillRepository.saveAndFlush(waybill)
        }
    }

    private fun buildRequestBody(
        waybill: Waybill,
        orderList: List<SubOrder>,
    ): String {
        val waybillRequests = orderList.flatMap { WaybillRequest.of(it, waybill) }
        val totalWeight = waybillRequests.sumOf { it.weight }
        val productList =
            waybillRequests.map { order ->
                mapOf(
                    "goodsNameEn" to order.name,
                    "goodsNameCh" to order.cnName,
                    "price" to order.price(waybillRequests.sumOf { it.qty }, order.country),
                    "quantity" to order.qty,
                    "weight" to (order.weight * BigDecimal(100)).setScale(0).toInt(),
                    "material" to order.material,
                    "hscode" to order.hsCode,
                )
            }

        val totalPrice =
            waybillRequests.sumOf { order ->
                order.price(waybillRequests.sumOf { it.qty }, order.country)
            }

        return JSON.toJSONString(
            mapOf(
                "channelId" to waybill.shipping.shipMethod,
                "orderNumber" to waybill.orderNo,
                "receiverInfo" to
                    mapOf(
                        "name" to waybill.recipient.receiverName,
                        "phone" to waybill.recipient.phone,
                        "country" to waybill.recipient.country,
                        "city" to waybill.recipient.city,
                        "address" to waybill.recipient.address(),
                        "zipCode" to waybill.recipient.postcode,
                        "state" to waybill.recipient.state,
                        "taxNumber" to waybill.taxNumber,
                    ),
                "parcelInfo" to
                    mapOf(
                        "hasBattery" to 0,
                        "currency" to "USD",
                        "totalPrice" to totalPrice,
                        "totalQuantity" to waybillRequests.sumOf { it.qty },
                        "totalWeight" to
                            (totalWeight * BigDecimal(100)).setScale(0).toInt(),
                        "productList" to productList,
                    ),
                "remark" to waybillRequests.joinToString(";") { it.orderNo + "-" + it.skuCode() },
            ),
        )
    }

    private fun sendRequest(
        requestBody: String,
        account: String,
        apiSecret: String,
        method: String,
    ): String {
        val timestamp = System.currentTimeMillis()
        val baseSecret = "$apiSecret$account${requestBody}json${method}${timestamp}V1.0$apiSecret"
        val sign = getMD5(baseSecret)

        val reqUrl =
            "$waybillUrl?user_id=$account&method=$method&format=json&timestamp=$timestamp&sign=$sign&version=V1.0"

        log.info { "YanWen API Request URL: $reqUrl" }
        log.info { "YanWen API Request Body: $requestBody" }

        val request =
            Request
                .Builder()
                .header("Content-Type", "application/json")
                .url(reqUrl)
                .post(requestBody.toRequestBody())
                .build()

        return okHttpClient.newCall(request).execute().use { response ->
            response.body?.string() ?: throw Exception("Empty response body")
        }
    }

    private fun handleResponse(
        responseBody: String,
        waybill: Waybill,
    ) {
        val response: YanWenCreateWaybillResponse = JSON.parseObject(responseBody)

        if (response.success) {
            val data = response.data
            waybill.status = WayBillStatus.PENDING
            waybill.waybillNo = data?.waybillNumber
            log.info { "YanWen 创建运单成功 | 订单号: ${waybill.orderNo} | 运单号: ${waybill.waybillNo}" }
            waybill.shipping.waybillLabelUrl = getPrintUrl(waybill)
            waybill.status = WayBillStatus.COMPLETED
            eventBus.publishEvent(WaybillCompletedEvent(waybill.id, waybill.subOrderId, waybill.orderNos))
            waybill.clearErrorMsg()
        } else {
            log.error { "YanWen 创建运单失败 | 订单号: ${waybill.orderNo} | 错误信息: ${response.message}" }
            waybill.failed(response.message ?: "Unknown error")
        }
        waybillRepository.saveAndFlush(waybill)
    }

    override fun deletePredication(waybill: Waybill): Boolean {
        if (waybill.waybillNo.isNullOrBlank()) {
            return true
        }
        val (account, apiSecret) = getAccountAndSecret(waybill.shipping.channel.name)
        val requestBody = JSON.toJSONString(mapOf("waybillNumber" to waybill.waybillNo))

        try {
            val response = sendRequest(requestBody, account, apiSecret, "express.order.cancel")
            log.info { "YanWen 取消预报响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }
            val cancelResponse: YanWenCreateWaybillResponse = JSON.parseObject(response)
            return cancelResponse.success
        } catch (e: Exception) {
            log.error(e) { "YanWen 取消预报失败 | 订单号: ${waybill.orderNo} | 错误信息: ${e.message}" }
            return false
        }
    }

    fun test(
        order: String,
        channel: String,
    ): String {
        val requestBody =
            JSON.toJSONString(mapOf("waybillNumber" to order, "printRemark" to 0))
        val (account, apiSecret) = getAccountAndSecret(channel)

        val response = sendRequest(requestBody, account, apiSecret, "express.order.label.get")
        val printResponse: YanWenPrintUrlResponse = JSON.parseObject(response)
        if (printResponse.success) {
            val base64String = printResponse.data?.base64String
            if (base64String != null) {
                val fileBytes = Base64.getDecoder().decode(base64String)
                val inputStream = ByteArrayInputStream(fileBytes)
                val fileName = "${UUID.randomUUID()}.pdf"
                return ossClient.uploadFile(
                    1,
                    "waybill/pdf/1/178912721614950441/$fileName",
                    inputStream,
                )
            }
        }
        return ""
    }

    override fun getPrintUrl(waybill: Waybill): String {
        val requestBody =
            JSON.toJSONString(mapOf("waybillNumber" to waybill.waybillNo, "printRemark" to 0))
        val (account, apiSecret) = getAccountAndSecret(waybill.shipping.channel.name)

        val response = sendRequest(requestBody, account, apiSecret, "express.order.label.get")
        log.info { "YanWen 获取打印URL响应 | 订单号: ${waybill.orderNo} | 响应参数: $response" }
        val printResponse: YanWenPrintUrlResponse = JSON.parseObject(response)
        if (printResponse.success) {
            val base64String = printResponse.data?.base64String
            if (base64String != null) {
                val fileBytes = Base64.getDecoder().decode(base64String)
                val inputStream = ByteArrayInputStream(fileBytes)
                val fileName = "${UUID.randomUUID()}.pdf"
                return ossClient.uploadFile(
                    waybill.bizId,
                    "waybill/pdf/${waybill.bizId}/${waybill.id}/$fileName",
                    inputStream,
                )
            }
        }
        return ""
    }

    private fun getAccountAndSecret(channel: String): Pair<String, String> =
        when (channel) {
            "YW_HZ" -> Pair(accountHz, apiSecretHz)
            "YW_QZ" -> Pair(accountQz, apiSecretQz)
            "YW_GZ" -> Pair(accountGz, apiSecretGz)
            "YW_YW" -> Pair(accountYw, apiSecretYw)
            else -> throw IllegalArgumentException("Invalid channel: $channel")
        }

    private fun getMD5(input: String): String {
        val md = MessageDigest.getInstance("MD5")
        val messageDigest = md.digest(input.toByteArray())
        val number = BigInteger(1, messageDigest)
        var hashText = number.toString(16)
        while (hashText.length < 32) {
            hashText = "0$hashText"
        }
        return hashText
    }

    companion object {
        private val log = KotlinLogging.logger {}
    }
}
